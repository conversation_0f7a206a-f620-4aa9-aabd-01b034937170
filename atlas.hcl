# Atlas configuration for XBIT CDN Service

# Define the environments
env "local" {
  # URL of the database to inspect and apply migrations to
  url = "postgres://postgres:password@localhost:5432/xbit_cdn_local?sslmode=disable"
  
  # URL of the Dev Database for schema diffing
  dev = "postgres://postgres:password@localhost:5433/atlas_dev_local?sslmode=disable"
  
  # Path to the migration directory
  migration {
    dir = "file://migrations"
  }
  
  # Define the desired schema state using Go models
  schema {
    src = "file://internal/model/schema.go"
  }
}

env "unstable" {
  # URL of the database to inspect and apply migrations to
  url = "postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=${DB_SSLMODE}"
  
  # URL of the Dev Database for schema diffing
  dev = "postgres://postgres:password@localhost:5433/atlas_dev_unstable?sslmode=disable"
  
  # Path to the migration directory
  migration {
    dir = "file://migrations"
  }
  
  # Define the desired schema state using Go models
  schema {
    src = "file://internal/model/schema.go"
  }
}

# Default environment
env = "local"
