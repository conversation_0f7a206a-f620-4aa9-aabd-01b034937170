#!/bin/bash

# Setup Atlas development databases for schema diffing
# This script creates temporary databases that Atlas uses for schema comparison

set -e

echo "Setting up Atlas development databases..."

# Default PostgreSQL connection parameters
POSTGRES_HOST=${POSTGRES_HOST:-localhost}
POSTGRES_PORT=${POSTGRES_PORT:-5433}
POSTGRES_USER=${POSTGRES_USER:-postgres}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}

# Database names for Atlas dev environments
DEV_DB_LOCAL="atlas_dev_local"
DEV_DB_UNSTABLE="atlas_dev_unstable"

# Function to create database if it doesn't exist
create_db_if_not_exists() {
    local db_name=$1
    echo "Creating database: $db_name"
    
    # Check if database exists
    if psql "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/postgres" \
           -tAc "SELECT 1 FROM pg_database WHERE datname='$db_name'" | grep -q 1; then
        echo "Database $db_name already exists, dropping and recreating..."
        psql "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/postgres" \
             -c "DROP DATABASE IF EXISTS $db_name;"
    fi
    
    psql "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/postgres" \
         -c "CREATE DATABASE $db_name;"
    
    echo "Database $db_name created successfully"
}

# Create development databases
create_db_if_not_exists $DEV_DB_LOCAL
create_db_if_not_exists $DEV_DB_UNSTABLE

echo "Atlas development databases setup completed!"
echo ""
echo "You can now use the following commands:"
echo "  make db-diff-local     - Generate migration diff for local environment"
echo "  make db-diff-unstable  - Generate migration diff for unstable environment"
echo "  make db-apply-local    - Apply migrations for local environment"
echo "  make db-apply-unstable - Apply migrations for unstable environment"
