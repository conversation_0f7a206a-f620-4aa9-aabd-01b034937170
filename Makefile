# XBIT CDN Service Makefile

# Variables
APP_NAME=xbit-cdn-service
DOCKER_IMAGE=$(APP_NAME)
DOCKER_TAG=latest
BINARY_NAME=bin/server

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build the application
.PHONY: build
build:
	$(GOBUILD) -o $(BINARY_NAME) cmd/server/main.go

# Run the application
.PHONY: run
run: build
	./$(BINARY_NAME)

# Run with hot reload (requires air: go install github.com/cosmtrek/air@latest)
.PHONY: dev
dev:
	air

# Clean build artifacts
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)

# Run tests
.PHONY: test
test:
	$(GOTEST) -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# Download dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Docker commands
.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 --env-file .env $(DOCKER_IMAGE):$(DOCKER_TAG)

# Environment-specific Docker Compose commands
.PHONY: local-up
local-up:
	docker-compose -f docker-compose.local.yml --env-file env/.env.local up -d

.PHONY: local-down
local-down:
	docker-compose -f docker-compose.local.yml down

.PHONY: local-logs
local-logs:
	docker-compose -f docker-compose.local.yml logs -f

.PHONY: unstable-up
unstable-up:
	docker-compose -f docker-compose.unstable.yml --env-file env/.env.unstable up -d

.PHONY: unstable-down
unstable-down:
	docker-compose -f docker-compose.unstable.yml down

.PHONY: unstable-logs
unstable-logs:
	docker-compose -f docker-compose.unstable.yml logs -f

.PHONY: staging-up
staging-up:
	docker-compose -f docker-compose.staging.yml --env-file env/.env.staging up -d

.PHONY: staging-down
staging-down:
	docker-compose -f docker-compose.staging.yml down

.PHONY: staging-logs
staging-logs:
	docker-compose -f docker-compose.staging.yml logs -f

.PHONY: production-up
production-up:
	docker-compose -f docker-compose.production.yml --env-file env/.env.production up -d

.PHONY: production-down
production-down:
	docker-compose -f docker-compose.production.yml down

.PHONY: production-logs
production-logs:
	docker-compose -f docker-compose.production.yml logs -f

# Legacy docker-compose commands (for backward compatibility)
.PHONY: docker-compose-up
docker-compose-up: local-up

.PHONY: docker-compose-down
docker-compose-down: local-down

.PHONY: docker-compose-logs
docker-compose-logs: local-logs

# Environment-specific Database commands
.PHONY: db-migrate-local
db-migrate-local:
	@echo "Running database migrations for local environment..."
	@if [ -f env/.env.local ]; then \
		export $$(cat env/.env.local | xargs) && \
		psql "postgresql://$$DB_USER:$$DB_PASSWORD@$$DB_HOST:$$DB_PORT/$$DB_NAME?sslmode=$$DB_SSLMODE" -f migrations/001_create_files_table.sql; \
	else \
		echo "Please create env/.env.local file with database configuration"; \
	fi

.PHONY: db-migrate-unstable
db-migrate-unstable:
	@echo "Running database migrations for unstable environment..."
	@if [ -f env/.env.unstable ]; then \
		export $$(cat env/.env.unstable | xargs) && \
		psql "postgresql://$$DB_USER:$$DB_PASSWORD@$$DB_HOST:$$DB_PORT/$$DB_NAME?sslmode=$$DB_SSLMODE" -f migrations/001_create_files_table.sql; \
	else \
		echo "Please create env/.env.unstable file with database configuration"; \
	fi

.PHONY: db-migrate-staging
db-migrate-staging:
	@echo "Running database migrations for staging environment..."
	@if [ -f env/.env.staging ]; then \
		export $$(cat env/.env.staging | xargs) && \
		psql "postgresql://$$DB_USER:$$DB_PASSWORD@$$DB_HOST:$$DB_PORT/$$DB_NAME?sslmode=$$DB_SSLMODE" -f migrations/001_create_files_table.sql; \
	else \
		echo "Please create env/.env.staging file with database configuration"; \
	fi

.PHONY: db-migrate-production
db-migrate-production:
	@echo "Running database migrations for production environment..."
	@if [ -f env/.env.production ]; then \
		export $$(cat env/.env.production | xargs) && \
		psql "postgresql://$$DB_USER:$$DB_PASSWORD@$$DB_HOST:$$DB_PORT/$$DB_NAME?sslmode=$$DB_SSLMODE" -f migrations/001_create_files_table.sql; \
	else \
		echo "Please create env/.env.production file with database configuration"; \
	fi

# Legacy database commands (for backward compatibility)
.PHONY: db-migrate
db-migrate: db-migrate-local

.PHONY: db-reset
db-reset:
	@echo "Resetting database..."
	@if [ -f env/.env.local ]; then \
		export $$(cat env/.env.local | xargs) && \
		psql "postgresql://$$DB_USER:$$DB_PASSWORD@$$DB_HOST:$$DB_PORT/$$DB_NAME?sslmode=$$DB_SSLMODE" -c "DROP TABLE IF EXISTS files CASCADE;"; \
		make db-migrate-local; \
	else \
		echo "Please create env/.env.local file with database configuration"; \
	fi

# Linting and formatting
.PHONY: fmt
fmt:
	$(GOCMD) fmt ./...

.PHONY: lint
lint:
	golangci-lint run

# Generate GraphQL code (if using gqlgen)
.PHONY: generate
generate:
	$(GOCMD) run github.com/99designs/gqlgen generate

# Install development tools
.PHONY: install-tools
install-tools:
	$(GOGET) github.com/cosmtrek/air@latest
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) github.com/99designs/gqlgen@latest

# Environment-specific setup commands
.PHONY: setup-local
setup-local: install-tools deps
	@echo "Setting up local development environment..."
	@if [ ! -f env/.env.local ]; then \
		mkdir -p env && \
		cp .env.local env/.env.local; \
		echo "Created env/.env.local - please configure your settings"; \
	fi
	@echo "Local development environment setup complete!"
	@echo "1. Configure env/.env.local with your settings"
	@echo "2. Run 'make local-up' to start dependencies"
	@echo "3. Run 'make db-migrate-local' to setup database"
	@echo "4. Run 'make dev' to start development server"

.PHONY: setup-unstable
setup-unstable:
	@echo "Setting up unstable environment..."
	@if [ ! -f env/.env.unstable ]; then \
		mkdir -p env && \
		cp .env.unstable env/.env.unstable; \
		echo "Created env/.env.unstable - please configure your settings"; \
	fi
	@echo "Unstable environment setup complete!"

.PHONY: setup-staging
setup-staging:
	@echo "Setting up staging environment..."
	@if [ ! -f env/.env.staging ]; then \
		mkdir -p env && \
		cp .env.staging env/.env.staging; \
		echo "Created env/.env.staging - please configure your settings"; \
	fi
	@echo "Staging environment setup complete!"

.PHONY: setup-production
setup-production:
	@echo "Setting up production environment..."
	@if [ ! -f env/.env.production ]; then \
		mkdir -p env && \
		cp .env.production env/.env.production; \
		echo "Created env/.env.production - please configure your settings"; \
	fi
	@echo "Production environment setup complete!"

# Legacy setup command (for backward compatibility)
.PHONY: setup
setup: setup-local

# Environment-specific deployment commands
.PHONY: deploy-unstable
deploy-unstable: docker-build
	@echo "Deploying to unstable environment..."
	@echo "This will be handled by DevOps CI/CD pipeline"

.PHONY: deploy-staging
deploy-staging: docker-build
	@echo "Deploying to staging environment..."
	@echo "This will be handled by DevOps CI/CD pipeline"

.PHONY: deploy-production
deploy-production: docker-build
	@echo "Deploying to production environment..."
	@echo "This will be handled by DevOps CI/CD pipeline"

# Legacy deploy command (for backward compatibility)
.PHONY: deploy
deploy: deploy-production

# Environment-specific health checks
.PHONY: health-local
health-local:
	@curl -f http://localhost:8080/health || echo "Local service is not healthy"

.PHONY: health-unstable
health-unstable:
	@curl -f https://unstable-api.xbit.com/health || echo "Unstable service is not healthy"

.PHONY: health-staging
health-staging:
	@curl -f https://staging-api.xbit.com/health || echo "Staging service is not healthy"

.PHONY: health-production
health-production:
	@curl -f https://api.xbit.com/health || echo "Production service is not healthy"

# Legacy health check (for backward compatibility)
.PHONY: health
health: health-local

# Show help
.PHONY: help
help:
	@echo "Available commands:"
	@echo ""
	@echo "Build & Run:"
	@echo "  build              Build the application"
	@echo "  run                Build and run the application"
	@echo "  dev                Run with hot reload (requires air)"
	@echo "  clean              Clean build artifacts"
	@echo ""
	@echo "Testing:"
	@echo "  test               Run tests"
	@echo "  test-coverage      Run tests with coverage report"
	@echo ""
	@echo "Dependencies:"
	@echo "  deps               Download and tidy dependencies"
	@echo "  install-tools      Install development tools"
	@echo ""
	@echo "Docker & Environment Management:"
	@echo "  docker-build       Build Docker image"
	@echo "  docker-run         Run Docker container"
	@echo "  local-up           Start local environment"
	@echo "  local-down         Stop local environment"
	@echo "  local-logs         Show local environment logs"
	@echo "  unstable-up        Start unstable environment"
	@echo "  unstable-down      Stop unstable environment"
	@echo "  unstable-logs      Show unstable environment logs"
	@echo "  staging-up         Start staging environment"
	@echo "  staging-down       Stop staging environment"
	@echo "  staging-logs       Show staging environment logs"
	@echo "  production-up      Start production environment"
	@echo "  production-down    Stop production environment"
	@echo "  production-logs    Show production environment logs"
	@echo ""
	@echo "Database Management:"
	@echo "  db-migrate-local   Run database migrations for local"
	@echo "  db-migrate-unstable Run database migrations for unstable"
	@echo "  db-migrate-staging Run database migrations for staging"
	@echo "  db-migrate-production Run database migrations for production"
	@echo "  db-reset           Reset local database and run migrations"
	@echo ""
	@echo "Environment Setup:"
	@echo "  setup-local        Setup local development environment"
	@echo "  setup-unstable     Setup unstable environment"
	@echo "  setup-staging      Setup staging environment"
	@echo "  setup-production   Setup production environment"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy-unstable    Deploy to unstable environment"
	@echo "  deploy-staging     Deploy to staging environment"
	@echo "  deploy-production  Deploy to production environment"
	@echo ""
	@echo "Health Checks:"
	@echo "  health-local       Check local service health"
	@echo "  health-unstable    Check unstable service health"
	@echo "  health-staging     Check staging service health"
	@echo "  health-production  Check production service health"
	@echo ""
	@echo "Code Quality:"
	@echo "  fmt                Format Go code"
	@echo "  lint               Run linter"
	@echo "  generate           Generate GraphQL code"
	@echo ""
	@echo "Legacy Commands (for backward compatibility):"
	@echo "  docker-compose-up  Start local environment (alias for local-up)"
	@echo "  docker-compose-down Stop local environment (alias for local-down)"
	@echo "  docker-compose-logs Show local environment logs (alias for local-logs)"
	@echo "  db-migrate         Run database migrations for local (alias for db-migrate-local)"
	@echo "  setup              Setup local development environment (alias for setup-local)"
	@echo "  deploy             Deploy to production (alias for deploy-production)"
	@echo "  health             Check local service health (alias for health-local)"
	@echo ""
	@echo "  help               Show this help message"
